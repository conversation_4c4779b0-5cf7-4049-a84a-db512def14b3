import type { Metadata } from "next"
import { DentistSidebar } from "@/components/layout/dentist-sidebar"

export const metadata: Metadata = {
  title: "Dentist Dashboard - LynnVill Dental Clinic",
  description: "Dentist dashboard for managing patients, appointments, and clinical records.",
}

export default function DentistLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <div className="flex h-screen">
        {/* Sidebar */}
        <DentistSidebar />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <main className="flex-1 overflow-y-auto">
            <div className="lg:pl-0 pl-16"> {/* Add padding for mobile menu button */}
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
