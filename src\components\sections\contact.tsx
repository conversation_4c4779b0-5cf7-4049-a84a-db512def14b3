import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  Calendar,
  MessageSquare,
  Navigation,
  Car,
  Accessibility,
  Wifi
} from "lucide-react"

const contactInfo = [
  {
    icon: Phone,
    title: "Phone",
    primary: "+63 ************",
    secondary: "Emergency: +63 ************",
    description: "Call us for appointments or emergencies"
  },
  {
    icon: Mail,
    title: "Email",
    primary: "<EMAIL>",
    secondary: "<EMAIL>",
    description: "Send us a message anytime"
  },
  {
    icon: MapPin,
    title: "Address",
    primary: "123 Rizal Avenue",
    secondary: "Makati City, Metro Manila 1200",
    description: "Easy to find location with parking"
  }
]

const hours = [
  { day: "Monday", time: "8:00 AM - 6:00 PM" },
  { day: "Tuesday", time: "8:00 AM - 6:00 PM" },
  { day: "Wednesday", time: "8:00 AM - 6:00 PM" },
  { day: "Thursday", time: "8:00 AM - 6:00 PM" },
  { day: "Friday", time: "8:00 AM - 6:00 PM" },
  { day: "Saturday", time: "9:00 AM - 3:00 PM" },
  { day: "Sunday", time: "Closed" }
]

const amenities = [
  { icon: Car, label: "Free Parking" },
  { icon: Accessibility, label: "Wheelchair Accessible" },
  { icon: Wifi, label: "Free WiFi" },
  { icon: MessageSquare, label: "Multilingual Staff" }
]

export function ContactSection() {
  return (
    <section id="contact" className="py-16 lg:py-24 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
            <MapPin className="h-4 w-4" />
            <span>Contact Us</span>
          </div>
          
          <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-4">
            Visit Our Modern Clinic
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Conveniently located in the heart of the city with easy access and plenty of parking. 
            We&apos;re here to serve you with exceptional dental care.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h3>
              
              <div className="space-y-6">
                {contactInfo.map((info, index) => {
                  const IconComponent = info.icon
                  return (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <IconComponent className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">{info.title}</h4>
                        <p className="text-gray-900 font-medium">{info.primary}</p>
                        <p className="text-gray-600 text-sm">{info.secondary}</p>
                        <p className="text-gray-500 text-sm mt-1">{info.description}</p>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Office Hours */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-primary" />
                  <span>Office Hours</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {hours.map((schedule, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-gray-700 font-medium">{schedule.day}</span>
                      <span className={`text-sm ${schedule.time === 'Closed' ? 'text-gray-500' : 'text-gray-900'}`}>
                        {schedule.time}
                      </span>
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-3 bg-primary/5 rounded-lg">
                  <p className="text-sm text-primary font-medium">
                    Emergency appointments available 24/7
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Amenities */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Clinic Amenities</h4>
              <div className="grid grid-cols-2 gap-4">
                {amenities.map((amenity, index) => {
                  const IconComponent = amenity.icon
                  return (
                    <div key={index} className="flex items-center space-x-2">
                      <IconComponent className="h-5 w-5 text-primary" />
                      <span className="text-sm text-gray-600">{amenity.label}</span>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>

          {/* Map and Quick Contact */}
          <div className="space-y-8">
            {/* Map Placeholder */}
            <Card>
              <CardContent className="p-0">
                <div className="aspect-[4/3] bg-gray-100 rounded-lg flex items-center justify-center relative overflow-hidden">
                  <div className="text-center">
                    <Navigation className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500 font-medium">Interactive Map</p>
                    <p className="text-sm text-gray-400">123 Dental Street, City, State</p>
                  </div>
                  
                  {/* Map overlay with directions button */}
                  <div className="absolute bottom-4 right-4">
                    <Button size="sm" className="bg-primary hover:bg-primary/90">
                      Get Directions
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Contact Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MessageSquare className="h-5 w-5 text-primary" />
                  <span>Quick Contact</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                      First Name
                    </label>
                    <Input id="firstName" placeholder="Your first name" />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                      Last Name
                    </label>
                    <Input id="lastName" placeholder="Your last name" />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <Input id="email" type="email" placeholder="<EMAIL>" />
                </div>
                
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone
                  </label>
                  <Input id="phone" type="tel" placeholder="(*************" />
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                    Message
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="How can we help you?"
                  />
                </div>
                
                <Button className="w-full bg-primary hover:bg-primary/90">
                  Send Message
                </Button>
                
                <p className="text-xs text-gray-500 text-center">
                  We&apos;ll get back to you within 24 hours
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Emergency Contact Banner */}
        <div className="bg-red-50 border border-red-200 rounded-2xl p-6 lg:p-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <h3 className="text-xl font-bold text-red-900 mb-2">
                Dental Emergency?
              </h3>
              <p className="text-red-700">
                Don&apos;t wait - call our emergency line for immediate assistance
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button variant="outline" className="border-red-300 text-red-700 hover:bg-red-50">
                <Phone className="h-4 w-4 mr-2" />
                Emergency Line
              </Button>
              <Button className="bg-red-600 hover:bg-red-700">
                <Calendar className="h-4 w-4 mr-2" />
                Book Urgent Appointment
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
