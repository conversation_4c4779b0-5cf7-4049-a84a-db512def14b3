"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Star, Quote, ChevronLeft, ChevronRight, Users } from "lucide-react"

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    age: 34,
    treatment: "Cosmetic Dentistry",
    rating: 5,
    text: "<PERSON><PERSON> and her team transformed my smile completely! I was always self-conscious about my teeth, but after getting veneers, I can't stop smiling. The entire process was comfortable and professional.",
    image: "/api/placeholder/80/80",
    location: "Makati"
  },
  {
    id: 2,
    name: "<PERSON>",
    age: 42,
    treatment: "Dental Implants",
    rating: 5,
    text: "After losing a tooth in an accident, I was worried about the implant process. <PERSON><PERSON> explained everything clearly and made me feel at ease. The result looks and feels completely natural!",
    image: "/api/placeholder/80/80",
    location: "Quezon City"
  },
  {
    id: 3,
    name: "<PERSON>",
    age: 28,
    treatment: "Orthodontics",
    rating: 5,
    text: "I got Invisalign treatment and couldn't be happier with the results. The staff was always helpful with scheduling, and <PERSON><PERSON> monitored my progress closely. My teeth are perfectly straight now!",
    image: "/api/placeholder/80/80",
    location: "Taguig"
  },
  {
    id: 4,
    name: "<PERSON> <PERSON>",
    age: 55,
    treatment: "General Dentistry",
    rating: 5,
    text: "I've been coming here for 5 years now. The preventive care is excellent, and they caught a small cavity early. The team is friendly, professional, and always makes me feel comfortable.",
    image: "/api/placeholder/80/80",
    location: "Pasig"
  },
  {
    id: 5,
    name: "<PERSON> Villanueva",
    age: 39,
    treatment: "Emergency Care",
    rating: 5,
    text: "When I had a dental emergency on a weekend, they saw me the same day! Dr. Reyes relieved my pain immediately and fixed the problem. I'm so grateful for their emergency services.",
    image: "/api/placeholder/80/80",
    location: "Mandaluyong"
  },
  {
    id: 6,
    name: "Miguel Torres",
    age: 31,
    treatment: "Teeth Whitening",
    rating: 5,
    text: "The professional whitening treatment gave me amazing results in just one session. My teeth are several shades whiter, and the process was completely painless. Highly recommend!",
    image: "/api/placeholder/80/80",
    location: "Ortigas"
  }
]

const reviewStats = [
  { platform: "Google", rating: 4.9, reviews: 287 },
  { platform: "Yelp", rating: 4.8, reviews: 156 },
  { platform: "Facebook", rating: 5.0, reviews: 89 }
]

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const testimonialsPerPage = 3

  const nextTestimonials = () => {
    setCurrentIndex((prev) => 
      prev + testimonialsPerPage >= testimonials.length ? 0 : prev + testimonialsPerPage
    )
  }

  const prevTestimonials = () => {
    setCurrentIndex((prev) => 
      prev === 0 ? Math.max(0, testimonials.length - testimonialsPerPage) : prev - testimonialsPerPage
    )
  }

  const currentTestimonials = testimonials.slice(currentIndex, currentIndex + testimonialsPerPage)

  const renderStars = (rating: number) => {
    return [...Array(5)].map((_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ))
  }

  return (
    <section id="testimonials" className="py-16 lg:py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Users className="h-4 w-4" />
            <span>Patient Reviews</span>
          </div>
          
          <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-4">
            What Our Patients Say
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Don&apos;t just take our word for it. Here&apos;s what our patients have to say about
            their experience at LynnVill Dental Clinic.
          </p>

          {/* Review Stats */}
          <div className="flex flex-wrap justify-center gap-6 mb-8">
            {reviewStats.map((stat, index) => (
              <div key={index} className="bg-white rounded-lg px-6 py-3 shadow-sm border">
                <div className="flex items-center space-x-2">
                  <span className="font-semibold text-gray-900">{stat.platform}</span>
                  <div className="flex">
                    {renderStars(Math.floor(stat.rating))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {stat.rating} ({stat.reviews} reviews)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Testimonials Grid */}
        <div className="relative">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            {currentTestimonials.map((testimonial) => (
              <Card key={testimonial.id} className="relative bg-white hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  {/* Quote Icon */}
                  <div className="absolute -top-3 left-6">
                    <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                      <Quote className="h-3 w-3 text-white" />
                    </div>
                  </div>

                  {/* Rating */}
                  <div className="flex items-center space-x-1 mb-4 pt-2">
                    {renderStars(testimonial.rating)}
                  </div>

                  {/* Testimonial Text */}
                  <p className="text-gray-600 leading-relaxed mb-6">
                    &ldquo;{testimonial.text}&rdquo;
                  </p>

                  {/* Patient Info */}
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-primary font-semibold">
                        {testimonial.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">
                        {testimonial.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {testimonial.treatment} • {testimonial.location}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Navigation Controls */}
          <div className="flex justify-center items-center space-x-4">
            <Button
              variant="outline"
              size="icon"
              onClick={prevTestimonials}
              className="rounded-full"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <div className="flex space-x-2">
              {Array.from({ length: Math.ceil(testimonials.length / testimonialsPerPage) }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index * testimonialsPerPage)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    Math.floor(currentIndex / testimonialsPerPage) === index 
                      ? 'bg-primary' 
                      : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
            
            <Button
              variant="outline"
              size="icon"
              onClick={nextTestimonials}
              className="rounded-full"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl p-8 lg:p-12 shadow-sm">
            <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
              Ready to Join Our Happy Patients?
            </h3>
            <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
              Experience the same exceptional care that our patients rave about. 
              Schedule your appointment today and see why we&apos;re the trusted choice for dental care.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                Book Your Appointment
              </Button>
              <Button variant="outline" size="lg">
                Read More Reviews
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
